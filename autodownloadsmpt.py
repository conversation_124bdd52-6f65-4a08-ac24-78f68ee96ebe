import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Select


# === KONFIGURASI ===
EMAIL = "<EMAIL>"  # Ganti dengan email login kamu
PASSWORD = "elok01"  # Ganti dengan password login kamu
FOLDER_PATH_DOWNLOAD = r"C:\Users\<USER>\Downloads\SPMT_CPNS_Signed"  # Folder tujuan download
TTE_LOGIN = "https://tte.kemenag.go.id"

# === SETUP DRIVER ===
options = webdriver.EdgeOptions()
options.use_chromium = True
driver = webdriver.Edge(options=options)
driver.get(TTE_LOGIN)

# === LOGIN ===
wait = WebDriverWait(driver, 20)
wait.until(EC.element_to_be_clickable((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[1]/div/div[3]/input'))).click()
wait.until(EC.presence_of_element_located((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[4]/div/input'))).send_keys(EMAIL)
wait.until(EC.presence_of_element_located((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[5]/div/input'))).send_keys(PASSWORD)
wait.until(EC.element_to_be_clickable((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[6]/div[2]/button'))).click()

# Tunggu login selesai
wait.until(EC.presence_of_element_located((By.XPATH, '//nav')))  # Tunggu hingga elemen navigasi muncul
print("✅ Login berhasil")

# === LOGIN ===
wait = WebDriverWait(driver, 20)
wait.until(EC.element_to_be_clickable((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[1]/div/div[3]/input'))).click()
wait.until(EC.presence_of_element_located((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[4]/div/input'))).send_keys(EMAIL)
wait.until(EC.presence_of_element_located((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[5]/div/input'))).send_keys(PASSWORD)
wait.until(EC.element_to_be_clickable((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[6]/div[2]/button'))).click()

# Tunggu login selesai
wait.until(EC.presence_of_element_located((By.XPATH, '//nav')))  # Tunggu hingga elemen navigasi muncul
print("✅ Login berhasil")

# === Akses Halaman Dokumen ===
driver.get("https://tte.kemenag.go.id/satker/dokumen/naskah/index/unggah")
wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="example2_length"]')))  # Tunggu elemen filter muncul

# === Pilih filter "100" data menggunakan XPath yang benar ===
filter_dropdown = wait.until(EC.element_to_be_clickable((By.XPATH, '/html/body/section/div/section/div[1]/div/section/div/div[2]/div/div[1]/div[1]/div/label/select')))
select = Select(filter_dropdown)
select.select_by_visible_text("100")  # Pilih "100"

# Tunggu refresh tabel selesai (beberapa detik agar tabel terupdate)
print("🔄 Menunggu pembaruan tabel setelah filter diterapkan...")
wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="example2"]/tbody')))  # Tunggu tabel terupdate
time.sleep(3)  # Sesuaikan jika perlu menunggu lebih lama

# === Looping untuk mencari dokumen "SPMT CPNS T.A 2024" dengan status "FINAL" ===
while True:
    # Cari elemen yang mengandung "SPMT CPNS T.A 2024" di kolom perihal dan status "FINAL" di kolom unduhan
    dokumen_elements = driver.find_elements(By.XPATH, "//table/tbody/tr[*]/td[3][contains(text(),'SPMT CPNS T.A 2024')]/following-sibling::td[5]//a[contains(text(),'FINAL')]")
    
    if len(dokumen_elements) == 0:
        print("❌ Tidak ada dokumen 'SPMT CPNS T.A 2024' dengan status 'FINAL' yang ditemukan.")
        break  # Keluar dari loop jika tidak ada dokumen lagi
    
    # Download dokumen yang ditemukan
    for dokumen in dokumen_elements:
        doc_url = dokumen.get_attribute("href")
        print(f"✅ Dokumen ditemukan: {doc_url}")
        
        # Buka dokumen di tab baru untuk mengunduhnya
        driver.execute_script(f"window.open('{doc_url}', '_blank');")
        driver.switch_to.window(driver.window_handles[-1])  # Pindah ke tab baru
        
        # Tunggu beberapa detik agar file dapat diunduh
        time.sleep(5)
        
        # Tutup tab setelah selesai mengunduh
        driver.close()
        driver.switch_to.window(driver.window_handles[0])  # Kembali ke tab utama

    # Menunggu beberapa detik sebelum mencari dokumen lagi (untuk menghindari terlalu cepat mengulang)
    time.sleep(2)
    
    # Cek apakah halaman berikutnya ada
    next_page_button = driver.find_elements(By.XPATH, '//a[@class="page-link" and contains(text(), "Next")]')
    if next_page_button:
        next_page_button[0].click()  # Klik tombol "Next" untuk halaman berikutnya
        print("➡️ Beralih ke halaman berikutnya...")
        time.sleep(2)
    else:
        print("✅ Semua dokumen telah diunduh.")
        break

# === Tutup Browser ===
driver.quit()