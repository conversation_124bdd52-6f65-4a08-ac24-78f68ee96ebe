import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Select


# === KONFIGURASI ===
EMAIL = "<EMAIL>"  # Ganti dengan email login kamu
PASSWORD = "elok01"  # Ganti dengan password login kamu
FOLDER_PATH_DOWNLOAD = r"C:\Users\<USER>\Downloads\SPMT_CPNS_Signed"  # Folder tujuan download
TTE_LOGIN = "https://tte.kemenag.go.id"

# === SETUP DRIVER ===
options = webdriver.EdgeOptions()
options.use_chromium = True

# Konfigurasi download
prefs = {
    "download.default_directory": FOLDER_PATH_DOWNLOAD,
    "download.prompt_for_download": False,
    "download.directory_upgrade": True,
    "safebrowsing.enabled": True
}
options.add_experimental_option("prefs", prefs)

# Pastikan folder download ada
import os
if not os.path.exists(FOLDER_PATH_DOWNLOAD):
    os.makedirs(FOLDER_PATH_DOWNLOAD)
    print(f"📁 Folder download dibuat: {FOLDER_PATH_DOWNLOAD}")

driver = webdriver.Edge(options=options)
driver.get(TTE_LOGIN)

# === LOGIN ===
wait = WebDriverWait(driver, 20)
wait.until(EC.element_to_be_clickable((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[1]/div/div[3]/input'))).click()
wait.until(EC.presence_of_element_located((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[4]/div/input'))).send_keys(EMAIL)
wait.until(EC.presence_of_element_located((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[5]/div/input'))).send_keys(PASSWORD)
wait.until(EC.element_to_be_clickable((By.XPATH, '/html/body/section/div/div[1]/div/div/div[2]/form/div[6]/div[2]/button'))).click()

# Tunggu login selesai
wait.until(EC.presence_of_element_located((By.XPATH, '//nav')))  # Tunggu hingga elemen navigasi muncul
print("✅ Login berhasil")

# === Akses Halaman Dokumen ===
driver.get("https://tte.kemenag.go.id/satker/dokumen/naskah/index/unggah")
wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="example2_length"]')))  # Tunggu elemen filter muncul

# === Pilih filter "100" data menggunakan XPath yang benar ===
filter_dropdown = wait.until(EC.element_to_be_clickable((By.XPATH, '/html/body/section/div/section/div[1]/div/section/div/div[2]/div/div[1]/div[1]/div/label/select')))
select = Select(filter_dropdown)
select.select_by_visible_text("100")  # Pilih "100"

# Tunggu refresh tabel selesai (beberapa detik agar tabel terupdate)
print("🔄 Menunggu pembaruan tabel setelah filter diterapkan...")
wait.until(EC.presence_of_element_located((By.XPATH, '/html/body/section/div/section/div[1]/div/section/div/div[2]/div/div[2]/div/table/tbody')))  # Tunggu tabel terupdate

print("✅ Tabel tbody ditemukan, menunggu 5 detik untuk memastikan data dimuat...")
time.sleep(5)  # Tunggu 5 detik seperti yang diminta

# === Looping untuk mencari dokumen "SPMT CPNS T.A 2024" dengan status "FINAL" ===
page_number = 1
while True:
    print(f"🔍 Memeriksa halaman {page_number}...")

    # Debug: Cek apakah tabel ada dan struktur header
    try:
        table_exists = driver.find_element(By.XPATH, "/html/body/section/div/section/div[1]/div/section/div/div[2]/div/div[2]/div/table")
        print("✅ Tabel ditemukan")

        # Debug: Cek header tabel untuk memastikan urutan kolom
        headers = driver.find_elements(By.XPATH, "/html/body/section/div/section/div[1]/div/section/div/div[2]/div/div[2]/div/table/thead/tr/th")
        print(f"📋 Jumlah kolom header: {len(headers)}")
        perihal_col = None
        unduh_col = None
        for i, header in enumerate(headers):
            # Ambil teks dari elemen dan sub-elemen untuk menangani struktur HTML yang kompleks
            header_text = header.text.strip()
            header_html = header.get_attribute('innerHTML')
            print(f"   Kolom {i+1}: '{header_text}' (HTML: {header_html[:50]}...)")

            # Cek berbagai variasi teks untuk kolom Perihal
            if any(keyword in header_text.upper() for keyword in ["PERIHAL", "DOKUMEN"]):
                perihal_col = i + 1
                print(f"   ✅ Kolom Perihal Dokumen ditemukan di posisi {i+1}")

            # Cek berbagai variasi teks untuk kolom Unduh
            if any(keyword in header_text.upper() for keyword in ["UNDUH", "DOWNLOAD"]):
                unduh_col = i + 1
                print(f"   ✅ Kolom Unduh ditemukan di posisi {i+1}")

    except Exception as e:
        print(f"❌ Tabel tidak ditemukan atau error: {str(e)}")
        break

    # Debug: Cek jumlah baris dalam tabel dan isi beberapa baris pertama
    try:
        rows = driver.find_elements(By.XPATH, "/html/body/section/div/section/div[1]/div/section/div/div[2]/div/div[2]/div/table/tbody/tr")
        print(f"📊 Jumlah baris dalam tabel: {len(rows)}")

        # Debug: Tampilkan isi 3 baris pertama untuk verifikasi
        for i, row in enumerate(rows[:3]):
            try:
                cells = row.find_elements(By.TAG_NAME, "td")
                if len(cells) >= 2:
                    perihal_text = cells[1].text  # Kolom ke-2 (index 1)
                    print(f"🔍 Baris {i+1} - Perihal: '{perihal_text}'")
                    if len(cells) >= 8:
                        unduh_cell = cells[7]  # Kolom ke-8 (index 7)
                        final_links = unduh_cell.find_elements(By.TAG_NAME, "a")
                        for link in final_links:
                            print(f"   📎 Link: '{link.text}'")
            except Exception as e:
                print(f"❌ Error membaca baris {i+1}: {str(e)}")
    except:
        print("❌ Tidak dapat menghitung baris tabel")

    # Cari elemen yang mengandung "SPMT CPNS T.A 2024" di kolom perihal dan status "FINAL" di kolom unduhan
    # Menggunakan posisi kolom yang dinamis berdasarkan header yang ditemukan
    dokumen_elements = []

    # Jika deteksi kolom gagal, gunakan asumsi berdasarkan screenshot (kolom 3 untuk Perihal, kolom 8 untuk Unduh)
    if not perihal_col or not unduh_col:
        print("⚠️ Deteksi kolom gagal, menggunakan asumsi berdasarkan screenshot:")
        perihal_col = 3  # Berdasarkan screenshot
        unduh_col = 8    # Berdasarkan screenshot
        print(f"   Menggunakan kolom Perihal: {perihal_col}, kolom Unduh: {unduh_col}")

    if perihal_col and unduh_col:
        print(f"🔍 Mencari dokumen menggunakan kolom Perihal: {perihal_col}, kolom Unduh: {unduh_col}")
        dokumen_elements = driver.find_elements(By.XPATH, f"/html/body/section/div/section/div[1]/div/section/div/div[2]/div/div[2]/div/table/tbody/tr[td[{perihal_col}][contains(text(),'SPMT CPNS T.A 2024')]]/td[{unduh_col}]//a[contains(text(),'FINAL')]")

        # Jika tidak ditemukan dengan XPath spesifik, coba dengan XPath yang lebih fleksibel
        if len(dokumen_elements) == 0:
            print("🔄 Mencoba dengan XPath alternatif 1...")
            dokumen_elements = driver.find_elements(By.XPATH, f"//table//tr[td[{perihal_col}][contains(text(),'SPMT CPNS T.A 2024')]]//a[contains(text(),'FINAL')]")

        # Jika masih tidak ditemukan, coba XPath yang lebih umum
        if len(dokumen_elements) == 0:
            print("🔄 Mencoba dengan XPath alternatif 2...")
            dokumen_elements = driver.find_elements(By.XPATH, "//tr[td[contains(text(),'SPMT CPNS T.A 2024')]]//a[contains(text(),'FINAL')]")

    # Jika masih tidak ditemukan atau kolom tidak teridentifikasi, coba mencari semua link FINAL dan filter manual
    if len(dokumen_elements) == 0:
        print("🔄 Mencoba dengan pendekatan manual...")
        all_rows = driver.find_elements(By.XPATH, "/html/body/section/div/section/div[1]/div/section/div/div[2]/div/div[2]/div/table/tbody/tr")
        dokumen_elements = []
        for row in all_rows:
            try:
                cells = row.find_elements(By.TAG_NAME, "td")
                # Cari di semua kolom untuk teks "SPMT CPNS T.A 2024"
                found_spmt = False
                for cell in cells:
                    if "SPMT CPNS T.A 2024" in cell.text:
                        found_spmt = True
                        print(f"✅ Ditemukan 'SPMT CPNS T.A 2024' di baris: {cell.text}")
                        break

                if found_spmt:
                    # Cari link FINAL di baris ini
                    final_links = row.find_elements(By.XPATH, ".//a[contains(text(),'FINAL')]")
                    if final_links:
                        dokumen_elements.extend(final_links)
                        print(f"✅ Ditemukan {len(final_links)} link FINAL di baris ini")
            except Exception as e:
                print(f"❌ Error saat memproses baris: {str(e)}")
                continue

    print(f"📋 Ditemukan {len(dokumen_elements)} dokumen dengan kriteria yang dicari")

    if len(dokumen_elements) == 0:
        print("❌ Tidak ada dokumen 'SPMT CPNS T.A 2024' dengan status 'FINAL' yang ditemukan pada halaman ini.")

        # Cek apakah halaman berikutnya ada
        next_page_button = driver.find_elements(By.XPATH, '//a[@class="page-link" and contains(text(), "Next")]')
        if next_page_button and next_page_button[0].is_enabled():
            next_page_button[0].click()  # Klik tombol "Next" untuk halaman berikutnya
            print("➡️ Beralih ke halaman berikutnya...")
            page_number += 1
            time.sleep(3)  # Tunggu halaman dimuat
            continue
        else:
            print("✅ Semua halaman telah diperiksa. Tidak ada dokumen yang ditemukan.")
            break

    # Download dokumen yang ditemukan
    for i, dokumen in enumerate(dokumen_elements):
        try:
            doc_url = dokumen.get_attribute("href")
            print(f"✅ Dokumen ke-{i+1} ditemukan: {doc_url}")

            # Cek apakah ini adalah link JavaScript atau URL biasa
            if doc_url and doc_url.startswith('http'):
                print(f"🔗 Link URL: {doc_url}")

                # Method 1: Klik langsung pada link
                print("🔄 Mencoba klik langsung...")
                try:
                    # Scroll ke elemen agar terlihat
                    driver.execute_script("arguments[0].scrollIntoView(true);", dokumen)
                    time.sleep(1)

                    # Klik menggunakan JavaScript untuk menghindari masalah overlay
                    driver.execute_script("arguments[0].click();", dokumen)
                    print("✅ Klik berhasil dengan JavaScript")

                    # Tunggu download dimulai
                    time.sleep(3)

                except Exception as click_error:
                    print(f"❌ Klik JavaScript gagal: {click_error}")

                    # Method 2: Buka di tab baru
                    print("🔄 Mencoba buka di tab baru...")
                    try:
                        driver.execute_script(f"window.open('{doc_url}', '_blank');")

                        # Tunggu tab baru terbuka
                        time.sleep(2)

                        if len(driver.window_handles) > 1:
                            driver.switch_to.window(driver.window_handles[-1])  # Pindah ke tab baru
                            print("✅ Tab baru terbuka")

                            # Tunggu download dimulai
                            time.sleep(5)

                            # Tutup tab
                            driver.close()
                            driver.switch_to.window(driver.window_handles[0])  # Kembali ke tab utama
                            print("✅ Tab ditutup, kembali ke tab utama")

                    except Exception as tab_error:
                        print(f"❌ Buka tab baru gagal: {tab_error}")

                        # Method 3: Navigasi langsung
                        print("🔄 Mencoba navigasi langsung...")
                        try:
                            current_url = driver.current_url
                            driver.get(doc_url)
                            time.sleep(5)  # Tunggu download
                            driver.get(current_url)  # Kembali ke halaman asal
                            time.sleep(2)  # Tunggu halaman dimuat
                            print("✅ Navigasi langsung berhasil")

                        except Exception as nav_error:
                            print(f"❌ Navigasi langsung gagal: {nav_error}")

            else:
                print(f"⚠️ Link tidak valid atau JavaScript: {doc_url}")
                # Untuk link JavaScript, coba klik langsung
                try:
                    driver.execute_script("arguments[0].scrollIntoView(true);", dokumen)
                    time.sleep(1)
                    driver.execute_script("arguments[0].click();", dokumen)
                    time.sleep(3)
                    print("✅ Klik JavaScript berhasil")
                except Exception as js_error:
                    print(f"❌ Klik JavaScript gagal: {js_error}")

            # Verifikasi apakah file berhasil didownload
            print("🔍 Memeriksa apakah file berhasil didownload...")

            # Cek file terbaru di folder download
            try:
                # Tunggu download selesai dan cek file terbaru
                time.sleep(5)  # Tunggu download selesai

                if os.path.exists(FOLDER_PATH_DOWNLOAD):
                    files = [f for f in os.listdir(FOLDER_PATH_DOWNLOAD) if os.path.isfile(os.path.join(FOLDER_PATH_DOWNLOAD, f))]
                    if files:
                        # Cari file terbaru
                        latest_file = max(files, key=lambda x: os.path.getmtime(os.path.join(FOLDER_PATH_DOWNLOAD, x)))
                        file_time = os.path.getmtime(os.path.join(FOLDER_PATH_DOWNLOAD, latest_file))
                        current_time = time.time()

                        if current_time - file_time < 60:  # File dibuat dalam 60 detik terakhir
                            file_size = os.path.getsize(os.path.join(FOLDER_PATH_DOWNLOAD, latest_file))
                            print(f"✅ File terbaru: {latest_file} (Size: {file_size} bytes)")

                            # Cek apakah file masih dalam proses download (biasanya ada .crdownload atau .tmp)
                            if not latest_file.endswith(('.crdownload', '.tmp', '.part')):
                                print("✅ Download kemungkinan berhasil")
                            else:
                                print("🔄 File masih dalam proses download...")
                        else:
                            print("⚠️ Tidak ada file baru yang terdeteksi dalam 60 detik terakhir")
                    else:
                        print("⚠️ Folder download kosong")
                else:
                    print("❌ Folder download tidak ditemukan")

            except Exception as verify_error:
                print(f"❌ Error saat verifikasi download: {verify_error}")

            print(f"📥 Proses download dokumen ke-{i+1} selesai")

        except Exception as e:
            print(f"❌ Error saat mengunduh dokumen ke-{i+1}: {str(e)}")
            # Pastikan kembali ke tab utama jika terjadi error
            try:
                if len(driver.window_handles) > 1:
                    driver.close()
                    driver.switch_to.window(driver.window_handles[0])
            except:
                pass

    # Menunggu beberapa detik sebelum mencari dokumen lagi (untuk menghindari terlalu cepat mengulang)
    time.sleep(2)

    # Cek apakah halaman berikutnya ada setelah download
    next_page_button = driver.find_elements(By.XPATH, '//a[@class="page-link" and contains(text(), "Next")]')
    if next_page_button and next_page_button[0].is_enabled():
        next_page_button[0].click()  # Klik tombol "Next" untuk halaman berikutnya
        print("➡️ Beralih ke halaman berikutnya...")
        page_number += 1
        time.sleep(3)
    else:
        print("✅ Semua dokumen telah diunduh.")
        break

# === Tutup Browser ===
driver.quit()